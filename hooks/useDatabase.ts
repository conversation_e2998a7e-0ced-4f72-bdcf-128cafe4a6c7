import { useCallback } from 'react';
import { useAuth } from './useAuth';
import { DatabaseService, PlantIdentification, PlantDiagnosis, UserProfile, RecoveryTracking } from '@/services/database';
import { Plant } from '@/types/plant';

/**
 * Hook that provides authenticated database operations
 * This ensures all database calls use the proper Clerk JWT token for RLS policies
 */
export const useDatabase = () => {
  const { getSupabaseClientWithValidation } = useAuth();

  // User Profile methods
  const getUserProfile = useCallback(async (userId: string): Promise<UserProfile | null> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.getUserProfile(userId, client);
  }, [getSupabaseClientWithValidation]);

  const createUserProfile = useCallback(async (profile: Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>): Promise<UserProfile | null> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.createUserProfile(profile, client);
  }, [getSupabaseClientWithValidation]);

  const updateUserProfile = useCallback(async (userId: string, updates: Partial<UserProfile>): Promise<UserProfile | null> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.updateUserProfile(userId, updates, client);
  }, [getSupabaseClientWithValidation]);

  const updateUserProfileStats = useCallback(async (userId: string): Promise<UserProfile | null> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.updateUserProfileStats(userId, client);
  }, [getSupabaseClientWithValidation]);

  // Plant Identification methods
  const createPlantIdentification = useCallback(async (identification: Omit<PlantIdentification, 'id' | 'created_at' | 'updated_at'>): Promise<PlantIdentification> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.createPlantIdentification(identification, client);
  }, [getSupabaseClientWithValidation]);

  const getPlantIdentification = useCallback(async (id: string): Promise<PlantIdentification | null> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.getPlantIdentification(id, client);
  }, [getSupabaseClientWithValidation]);

  const getPlantIdentifications = useCallback(async (userId: string): Promise<PlantIdentification[]> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.getPlantIdentifications(userId, client);
  }, [getSupabaseClientWithValidation]);

  const getRecentPlantIdentifications = useCallback(async (userId: string, limit: number = 5): Promise<PlantIdentification[]> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.getRecentPlantIdentifications(userId, limit, client);
  }, [getSupabaseClientWithValidation]);

  const updatePlantIdentification = useCallback(async (id: string, updates: Partial<PlantIdentification>): Promise<PlantIdentification | null> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.updatePlantIdentification(id, updates, client);
  }, [getSupabaseClientWithValidation]);

  const deletePlantIdentification = useCallback(async (id: string): Promise<boolean> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.deletePlantIdentification(id, client);
  }, [getSupabaseClientWithValidation]);

  // Plant Diagnosis methods
  const createPlantDiagnosis = useCallback(async (diagnosis: Omit<PlantDiagnosis, 'id' | 'created_at' | 'updated_at'>): Promise<PlantDiagnosis> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.createPlantDiagnosis(diagnosis, client);
  }, [getSupabaseClientWithValidation]);

  const getPlantDiagnosis = useCallback(async (id: string): Promise<PlantDiagnosis | null> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.getPlantDiagnosis(id, client);
  }, [getSupabaseClientWithValidation]);

  const getPlantDiagnoses = useCallback(async (userId: string): Promise<PlantDiagnosis[]> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.getPlantDiagnoses(userId, client);
  }, [getSupabaseClientWithValidation]);

  const getRecentPlantDiagnoses = useCallback(async (userId: string, limit: number = 5): Promise<PlantDiagnosis[]> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.getRecentPlantDiagnoses(userId, limit, client);
  }, [getSupabaseClientWithValidation]);

  const updatePlantDiagnosis = useCallback(async (id: string, updates: Partial<PlantDiagnosis>): Promise<PlantDiagnosis | null> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.updatePlantDiagnosis(id, updates, client);
  }, [getSupabaseClientWithValidation]);

  const deletePlantDiagnosis = useCallback(async (id: string): Promise<boolean> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.deletePlantDiagnosis(id, client);
  }, [getSupabaseClientWithValidation]);

  const updateDiagnosisNotes = useCallback(async (diagnosisId: string, notes: string): Promise<PlantDiagnosis> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.updateDiagnosisNotes(diagnosisId, notes, client);
  }, [getSupabaseClientWithValidation]);

  // Garden Collection methods removed - garden data is now stored directly in plant_identifications and plant_diagnoses tables

  // High-level methods for plant operations
  const createIdentificationAndAddToGarden = useCallback(async (plant: Plant, imageUri: string, userId: string, notes?: string, location?: string): Promise<PlantIdentification> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.createIdentificationAndAddToGarden(plant, imageUri, userId, notes, location, client);
  }, [getSupabaseClientWithValidation]);

  const createDiagnosisOnly = useCallback(async (plant: Plant, imageUri: string, userId: string, diagnosisData: any, notes?: string, location?: string): Promise<PlantDiagnosis> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.createDiagnosisOnly(plant, imageUri, userId, diagnosisData, notes, location, client);
  }, [getSupabaseClientWithValidation]);

  const shareIdentificationOnly = useCallback(async (plant: Plant, imageUri: string, userId: string): Promise<PlantIdentification> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.shareIdentificationOnly(plant, imageUri, userId, client);
  }, [getSupabaseClientWithValidation]);

  const shareDiagnosisOnly = useCallback(async (plant: Plant, imageUri: string, userId: string, diagnosisData: any): Promise<PlantDiagnosis> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.shareDiagnosisOnly(plant, imageUri, userId, diagnosisData, client);
  }, [getSupabaseClientWithValidation]);

  // addToGardenAndShare method removed - use shareIdentificationOnly or shareDiagnosisOnly instead

  // Recovery Tracking methods
  const createRecoveryTracking = useCallback(async (tracking: Omit<RecoveryTracking, 'id' | 'created_at' | 'updated_at'>): Promise<RecoveryTracking | null> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.createRecoveryTracking(tracking, client);
  }, [getSupabaseClientWithValidation]);

  const getRecoveryTracking = useCallback(async (diagnosisId: string): Promise<RecoveryTracking[]> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.getRecoveryTracking(diagnosisId, client);
  }, [getSupabaseClientWithValidation]);

  const updateRecoveryTracking = useCallback(async (id: string, updates: Partial<RecoveryTracking>): Promise<RecoveryTracking | null> => {
    const client = await getSupabaseClientWithValidation();
    return DatabaseService.updateRecoveryTracking(id, updates, client);
  }, [getSupabaseClientWithValidation]);

  return {
    // User Profile methods
    getUserProfile,
    createUserProfile,
    updateUserProfile,
    updateUserProfileStats,
    
    // Plant Identification methods
    createPlantIdentification,
    getPlantIdentification,
    getPlantIdentifications,
    getRecentPlantIdentifications,
    updatePlantIdentification,
    deletePlantIdentification,

    // Plant Diagnosis methods
    createPlantDiagnosis,
    getPlantDiagnosis,
    getPlantDiagnoses,
    getRecentPlantDiagnoses,
    updatePlantDiagnosis,
    deletePlantDiagnosis,
    updateDiagnosisNotes,
    
    // High-level methods
    createIdentificationAndAddToGarden,
    createDiagnosisOnly,
    shareIdentificationOnly,
    shareDiagnosisOnly,
    
    // Recovery Tracking methods
    createRecoveryTracking,
    getRecoveryTracking,
    updateRecoveryTracking,
  };
};
